using System;
using System.IO;

namespace BowelSoundLabeler.Models
{
    /// <summary>
    /// 音频文件模型
    /// </summary>
    public class AudioFile
    {
        public string FilePath { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FileExtension { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime ModifiedTime { get; set; }
        public bool IsValid { get; set; }
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 从文件路径创建AudioFile实例
        /// </summary>
        public static AudioFile FromPath(string filePath)
        {
            var fileInfo = new FileInfo(filePath);
            return new AudioFile
            {
                FilePath = filePath,
                FileName = Path.GetFileNameWithoutExtension(filePath),
                FileExtension = fileInfo.Extension,
                FileSize = fileInfo.Length,
                CreatedTime = fileInfo.CreationTime,
                ModifiedTime = fileInfo.LastWriteTime,
                IsValid = IsValidAudioFile(fileInfo.Extension)
            };
        }

        /// <summary>
        /// 检查文件扩展名是否为支持的音频格式
        /// </summary>
        private static bool IsValidAudioFile(string extension)
        {
            var supportedExtensions = new[] { ".wav", ".mp3", ".m4a", ".flac", ".mat" };
            return Array.Exists(supportedExtensions, ext => 
                string.Equals(ext, extension, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 获取显示名称
        /// </summary>
        public string DisplayName => $"{FileName}{FileExtension}";

        /// <summary>
        /// 获取文件大小的友好显示
        /// </summary>
        public string FileSizeDisplay
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F1} KB";
                if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024.0 * 1024.0):F1} MB";
                return $"{FileSize / (1024.0 * 1024.0 * 1024.0):F1} GB";
            }
        }
    }
}
