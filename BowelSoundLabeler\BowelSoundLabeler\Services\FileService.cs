using BowelSoundLabeler.Models;
using Microsoft.Extensions.Logging;
using Windows.Storage;
using Windows.Storage.Pickers;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 文件服务实现
    /// </summary>
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;
        private readonly IReadOnlyList<string> _supportedExtensions = new[]
        {
            ".wav", ".mp3", ".m4a", ".flac", ".mat", ".csv"
        };

        public FileService(ILogger<FileService> logger)
        {
            _logger = logger;
        }

        public async Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();
                
                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;
                picker.SuggestedStartLocation = PickerLocationId.MusicLibrary;

                // 如果提供了初始文件夹，尝试设置
                if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                {
                    try
                    {
                        var folder = await StorageFolder.GetFolderFromPathAsync(initialFolder);
                        picker.SuggestedStartLocation = PickerLocationId.Unspecified;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "无法设置初始文件夹: {Folder}", initialFolder);
                    }
                }

                var file = await picker.PickSingleFileAsync();
                if (file != null)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    _logger.LogInformation("选择了文件: {FilePath}", audioFile.FilePath);
                    return audioFile;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择单个文件时发生错误");
                throw;
            }
        }

        public async Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();
                
                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;
                picker.SuggestedStartLocation = PickerLocationId.MusicLibrary;

                // 如果提供了初始文件夹，尝试设置
                if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                {
                    try
                    {
                        var folder = await StorageFolder.GetFolderFromPathAsync(initialFolder);
                        picker.SuggestedStartLocation = PickerLocationId.Unspecified;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "无法设置初始文件夹: {Folder}", initialFolder);
                    }
                }

                var files = await picker.PickMultipleFilesAsync();
                var audioFiles = new List<AudioFile>();

                foreach (var file in files)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    audioFiles.Add(audioFile);
                }

                _logger.LogInformation("选择了 {Count} 个文件", audioFiles.Count);
                return audioFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择多个文件时发生错误");
                throw;
            }
        }

        public async Task<bool> ValidateAudioFileAsync(AudioFile audioFile)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(audioFile.FilePath))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "文件不存在";
                    return false;
                }

                if (!_supportedExtensions.Contains(audioFile.FileExtension, StringComparer.OrdinalIgnoreCase))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "不支持的文件格式";
                    return false;
                }

                audioFile.IsValid = true;
                audioFile.ErrorMessage = null;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证音频文件时发生错误: {FilePath}", audioFile.FilePath);
                audioFile.IsValid = false;
                audioFile.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(oldPath))
                {
                    throw new FileNotFoundException($"源文件不存在: {oldPath}");
                }

                if (File.Exists(newPath) && !overwrite)
                {
                    throw new InvalidOperationException($"目标文件已存在: {newPath}");
                }

                File.Move(oldPath, newPath, overwrite);
                _logger.LogInformation("文件重命名成功: {OldPath} -> {NewPath}", oldPath, newPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件时发生错误: {OldPath} -> {NewPath}", oldPath, newPath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            await Task.CompletedTask; // 保持异步接口一致性
            return File.Exists(filePath);
        }

        public IReadOnlyList<string> GetSupportedExtensions()
        {
            return _supportedExtensions;
        }
    }
}
