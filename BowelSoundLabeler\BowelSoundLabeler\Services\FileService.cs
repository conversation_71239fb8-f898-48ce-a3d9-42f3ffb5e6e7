using BowelSoundLabeler.Models;
using Microsoft.Extensions.Logging;
using Windows.Storage;
using Windows.Storage.Pickers;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.WindowsRuntime;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 文件服务实现
    /// </summary>
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;
        private readonly IReadOnlyList<string> _supportedExtensions = new[]
        {
            ".wav", ".mp3", ".m4a", ".flac", ".mat", ".csv"
        };

        public FileService(ILogger<FileService> logger)
        {
            _logger = logger;
        }

        public async Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();

                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;

                // 设置文件选择器标识符，让系统记住用户的选择
                picker.SettingsIdentifier = "BowelSoundLabeler_SingleFile";
                picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 如果提供了初始文件夹，尝试通过多种方式设置
                if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                {
                    try
                    {
                        // 方法1：尝试通过StorageFolder设置
                        var folder = await StorageFolder.GetFolderFromPathAsync(initialFolder);

                        // 方法2：将文件夹添加到FutureAccessList，这样系统会记住它
                        Windows.Storage.AccessCache.StorageApplicationPermissions.FutureAccessList.AddOrReplace("DefaultFolder", folder);

                        // 方法3：设置为最近使用的位置
                        Windows.Storage.AccessCache.StorageApplicationPermissions.MostRecentlyUsedList.AddOrReplace("RecentFolder", folder);

                        // 设置提示文本让用户知道默认文件夹
                        var folderName = Path.GetFileName(initialFolder);
                        if (string.IsNullOrEmpty(folderName))
                            folderName = initialFolder;
                        picker.CommitButtonText = $"选择 (默认: {folderName})";

                        _logger.LogInformation("已设置默认文件夹提示: {Folder}", initialFolder);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "无法设置初始文件夹: {Folder}", initialFolder);
                        picker.CommitButtonText = "选择";
                    }
                }
                else
                {
                    picker.CommitButtonText = "选择";
                }

                var file = await picker.PickSingleFileAsync();
                if (file != null)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    _logger.LogInformation("选择了文件: {FilePath}", audioFile.FilePath);
                    return audioFile;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择单个文件时发生错误");
                throw;
            }
        }

        public async Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();

                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;

                // 设置文件选择器标识符，让系统记住用户的选择
                picker.SettingsIdentifier = "BowelSoundLabeler_MultipleFiles";
                picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 如果提供了初始文件夹，尝试通过多种方式设置
                if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
                {
                    try
                    {
                        // 方法1：尝试通过StorageFolder设置
                        var folder = await StorageFolder.GetFolderFromPathAsync(initialFolder);

                        // 方法2：将文件夹添加到FutureAccessList，这样系统会记住它
                        Windows.Storage.AccessCache.StorageApplicationPermissions.FutureAccessList.AddOrReplace("DefaultFolder", folder);

                        // 方法3：设置为最近使用的位置
                        Windows.Storage.AccessCache.StorageApplicationPermissions.MostRecentlyUsedList.AddOrReplace("RecentFolder", folder);

                        // 设置提示文本让用户知道默认文件夹
                        var folderName = Path.GetFileName(initialFolder);
                        if (string.IsNullOrEmpty(folderName))
                            folderName = initialFolder;
                        picker.CommitButtonText = $"选择 (默认: {folderName})";

                        _logger.LogInformation("已设置默认文件夹提示: {Folder}", initialFolder);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "无法设置初始文件夹: {Folder}", initialFolder);
                        picker.CommitButtonText = "选择";
                    }
                }
                else
                {
                    picker.CommitButtonText = "选择";
                }

                var files = await picker.PickMultipleFilesAsync();
                var audioFiles = new List<AudioFile>();

                foreach (var file in files)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    audioFiles.Add(audioFile);
                }

                _logger.LogInformation("选择了 {Count} 个文件", audioFiles.Count);
                return audioFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "选择多个文件时发生错误");
                throw;
            }
        }

        public async Task<bool> ValidateAudioFileAsync(AudioFile audioFile)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(audioFile.FilePath))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "文件不存在";
                    return false;
                }

                if (!_supportedExtensions.Contains(audioFile.FileExtension, StringComparer.OrdinalIgnoreCase))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "不支持的文件格式";
                    return false;
                }

                audioFile.IsValid = true;
                audioFile.ErrorMessage = null;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证音频文件时发生错误: {FilePath}", audioFile.FilePath);
                audioFile.IsValid = false;
                audioFile.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(oldPath))
                {
                    throw new FileNotFoundException($"源文件不存在: {oldPath}");
                }

                if (File.Exists(newPath) && !overwrite)
                {
                    throw new InvalidOperationException($"目标文件已存在: {newPath}");
                }

                File.Move(oldPath, newPath, overwrite);
                _logger.LogInformation("文件重命名成功: {OldPath} -> {NewPath}", oldPath, newPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件时发生错误: {OldPath} -> {NewPath}", oldPath, newPath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            await Task.CompletedTask; // 保持异步接口一致性
            return File.Exists(filePath);
        }

        public IReadOnlyList<string> GetSupportedExtensions()
        {
            return _supportedExtensions;
        }
    }
}
