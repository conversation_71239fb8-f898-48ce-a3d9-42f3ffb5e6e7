﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.windows.sdk.buildtools\10.0.26100.4948\buildTransitive\Microsoft.Windows.SDK.BuildTools.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windows.sdk.buildtools\10.0.26100.4948\buildTransitive\Microsoft.Windows.SDK.BuildTools.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.web.webview2\1.0.2903.40\buildTransitive\Microsoft.Web.WebView2.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.web.webview2\1.0.2903.40\buildTransitive\Microsoft.Web.WebView2.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.windowsappsdk\1.7.250606001\buildTransitive\Microsoft.WindowsAppSDK.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.windowsappsdk\1.7.250606001\buildTransitive\Microsoft.WindowsAppSDK.targets')" />
  </ImportGroup>
</Project>